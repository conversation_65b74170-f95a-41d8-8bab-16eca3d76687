name: matlab-team
version: 1.0.0
description: Comprehensive MATLAB development team with specialized agents for programming, algorithm design, simulation, and data analysis

# Team composition and roles
agents:
  orchestrator:
    id: matlab-orchestrator
    name: Dr. <PERSON>
    role: Senior MATLAB Technical Lead
    responsibilities:
      - Project coordination and management
      - Team workflow orchestration
      - Quality assurance and reviews
      - Stakeholder communication
      - Resource allocation and planning
    
  specialists:
    - id: matlab-programmer
      name: <PERSON>
      role: MATLAB Programming Specialist
      responsibilities:
        - Code development and implementation
        - Code review and quality assurance
        - Performance optimization
        - Debugging and troubleshooting
        - Unit testing and validation
      
    - id: algorithm-designer
      name: Dr. <PERSON>
      role: Algorithm Development Expert
      responsibilities:
        - Mathematical modeling and analysis
        - Algorithm design and optimization
        - Complexity analysis and validation
        - Performance benchmarking
        - Research and innovation
      
    - id: simulation-modeler
      name: <PERSON>
      role: Simulink Modeling Specialist
      responsibilities:
        - System modeling and simulation
        - Simulink model design and implementation
        - Model validation and verification
        - Performance optimization
        - Integration with MATLAB code
      
    - id: data-analyst
      name: <PERSON>
      role: MATLAB Data Analysis Expert
      responsibilities:
        - Data analysis and statistical modeling
        - Statistical visualization and reporting
        - Exploratory data analysis
        - Machine learning and predictive modeling
        - Results interpretation and insights

    - id: visualization-specialist
      name: Dr. <PERSON>
      role: MATLAB Visualization & Graphics Expert
      responsibilities:
        - Data visualization design and implementation
        - Publication-quality figure creation
        - Interactive dashboard development
        - 3D visualization and animation
        - Visual style guide development

# Supported workflows
workflows:
  - matlab-development-workflow
  - algorithm-development-workflow
  - data-analysis-workflow
  - simulation-modeling-workflow

# Team collaboration patterns
collaboration:
  primary_handoffs:
    - from: matlab-orchestrator
      to: [matlab-programmer, algorithm-designer, simulation-modeler, data-analyst, visualization-specialist]
      trigger: Project assignment and task delegation
      
    - from: algorithm-designer
      to: matlab-programmer
      trigger: Algorithm specification complete
      
    - from: simulation-modeler
      to: matlab-programmer
      trigger: Model design complete
      
    - from: data-analyst
      to: matlab-programmer
      trigger: Analysis requirements defined

    - from: data-analyst
      to: visualization-specialist
      trigger: Data analysis complete, visualization needed

    - from: visualization-specialist
      to: matlab-programmer
      trigger: Visualization design complete, implementation needed

    - from: matlab-programmer
      to: matlab-orchestrator
      trigger: Implementation complete
      
  review_cycles:
    - participants: [matlab-orchestrator, matlab-programmer]
      frequency: Weekly
      purpose: Code quality and progress review
      
    - participants: [algorithm-designer, matlab-programmer]
      frequency: As needed
      purpose: Algorithm implementation review
      
    - participants: [simulation-modeler, matlab-programmer]
      frequency: As needed
      purpose: Model implementation review

# Quality assurance framework
quality_framework:
  code_reviews:
    required_reviewers: 2
    review_criteria:
      - Code quality and standards
      - Performance optimization
      - Documentation completeness
      - Test coverage
      
  quality_gates:
    - stage: Design
      reviewers: [matlab-orchestrator, relevant_specialist]
      criteria: [design_completeness, feasibility, resource_allocation]
      
    - stage: Implementation
      reviewers: [matlab-programmer, matlab-orchestrator]
      criteria: [code_quality, performance, testing, documentation]
      
    - stage: Validation
      reviewers: [all_team_members]
      criteria: [requirements_validation, performance_validation, user_acceptance]

# Communication protocols
communication:
  daily_standups:
    participants: [matlab-orchestrator, active_specialists]
    duration: 15 minutes
    format: Progress, blockers, next steps
    
  weekly_reviews:
    participants: [all_team_members]
    duration: 60 minutes
    format: Progress review, quality metrics, planning
    
  milestone_reviews:
    participants: [all_team_members, stakeholders]
    duration: 120 minutes
    format: Comprehensive review, demonstrations, planning

# Tools and infrastructure
tools:
  development:
    - MATLAB/Simulink
    - MATLAB Toolboxes (as required)
    - Version control (Git)
    - Code analysis tools
    
  collaboration:
    - Documentation platform
    - Issue tracking system
    - Communication tools
    - File sharing system
    
  quality_assurance:
    - Code review tools
    - Testing frameworks
    - Performance profiling tools
    - Documentation generators

# Performance metrics
metrics:
  team_performance:
    - Project delivery time
    - Quality gate pass rates
    - Code review efficiency
    - Stakeholder satisfaction
    
  individual_performance:
    - Task completion rates
    - Code quality scores
    - Review participation
    - Knowledge sharing contributions
    
  quality_metrics:
    - Defect rates
    - Performance benchmarks
    - Documentation completeness
    - Test coverage percentages

# Escalation procedures
escalation:
  technical_issues:
    level_1: Specialist consultation
    level_2: Team technical review
    level_3: External expert consultation
    
  resource_conflicts:
    level_1: Orchestrator mediation
    level_2: Stakeholder involvement
    level_3: Management escalation
    
  quality_concerns:
    level_1: Peer review and feedback
    level_2: Team quality review
    level_3: Process improvement initiative

# Training and development
development:
  continuous_learning:
    - Regular MATLAB training updates
    - Best practices sharing sessions
    - Technical conference participation
    - Cross-functional skill development
    
  knowledge_sharing:
    - Weekly technical presentations
    - Code review learning sessions
    - Documentation of lessons learned
    - Mentoring programs
